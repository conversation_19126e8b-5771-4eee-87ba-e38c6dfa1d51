BOT_NAME = "qidian"

SPIDER_MODULES = ["qidian.spiders"]
NEWSPIDER_MODULE = "qidian.spiders"

ADDONS = {}


# Obey robots.txt rules
ROBOTSTXT_OBEY = True


# Set settings whose default value is deprecated to a future-proof value
FEED_EXPORT_ENCODING = "utf-8"


ITEM_PIPELINES = {
   'quotes_scraper.pipelines.PostgresPipeline': 300,
}

# --- PostgreSQL 数据库配置 ---
POSTGRES_HOST = '**************'        # 数据库主机地址
POSTGRES_DB = 'demo'          # 数据库名称
POSTGRES_USER = 'postgres'    # 数据库用户名
POSTGRES_PASSWORD = '123456'  # 数据库密码